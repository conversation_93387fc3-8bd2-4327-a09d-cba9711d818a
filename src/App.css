/* 全局样式重置和基础设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #e2e4e7 0%, #c6c8ca 100%);
  color: #2c3e50;
  line-height: 1.6;
  min-height: 100vh;
}

#root {
  max-width: 650px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
  min-height: 100vh;
}

/* 标题样式 */
h1 {
  text-align: center;
  margin-bottom: 2.5rem;
  font-size: 2.8rem;
  font-weight: 200;
  color: #34495e;
  letter-spacing: -1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2.5rem;
}
.login-form input {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 48px;
  max-height: 120px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.login-form button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}
.login-form button:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}
/* 输入区域样式 */
.input-todo {
  display: flex;
  gap: 1rem;
  margin-bottom: 2.5rem;
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.todo-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 48px;
  max-height: 120px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.todo-input:focus {
  outline: none;
  border-color: #6c757d;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.15);
  transform: translateY(-2px);
}

.todo-input::placeholder {
  color: #adb5bd;
}

.add-todo {
  padding: 1rem 2rem;
  margin-top: 10px;
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}

.add-todo:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.add-todo:active {
  transform: translateY(0);
}

/* Todo列表样式 */
.todo-list ul {
  list-style: none;
  overflow: visible;
}

.todo-list {
  overflow: visible;
}

.todo-list li {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(233, 236, 239, 0.5);
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: visible;
}

.todo-list li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6c757d, #adb5bd);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.todo-list li:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(108, 117, 125, 0.3);
  transform: translateY(-4px);
}

.todo-list li:hover::before {
  opacity: 0;
}

/* 复选框样式 */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #6c737d;
  border-radius: 4px;
}

/* Todo文本样式 */
.todo-list span {
  flex: 1;
  font-size: 1rem;
  word-break: break-word;
  transition: all 0.3s ease;
}

.todo-list span.done {
  text-decoration: line-through;
  color: #6c757d;
  opacity: 0.6;
}

/* 编辑输入框样式 */
.todo-list input[type="text"] {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #6c757d;
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.todo-list input[type="text"]:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.15);
  transform: translateY(-1px);
}

/* 按钮样式 */
.todo-list button {
  padding: 0.6rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  color: #495057;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
}

.todo-list button:hover {
  background: rgba(248, 249, 250, 0.95);
  border-color: #adb5bd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.todo-list button:active {
  transform: translateY(0);
}

.todo-list li button:last-child {
  background: linear-gradient(135deg, #868e96 0%, #6c757d 100%);
  color: white;
  border-color: #868e96;
}

.todo-list li button:last-child:hover {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border-color: #6c757d;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 保存和取消按钮样式 */
.todo-list li button:nth-last-child(2) {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  border-color: #6c757d;
}

.todo-list li button:nth-last-child(2):hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 三点菜单样式 */
.menu-container {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 10;
}

/* 当菜单打开时，提升整个容器的z-index */
.menu-container.menu-open {
  z-index: 10000;
}

/* 确保打开菜单的todo项在最上层 */
.todo-list li.menu-active {
  z-index: 10000;
  position: relative;
}

.user-info button {
  padding: 0.5rem 1rem;
  border: 1px solid #414e5b;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.9);}

.user-info button:hover {
  background: rgba(248, 249, 250, 0.95);
  border-color: #adb5bd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-button {
  background: none !important;
  border: none !important;
  font-size: 1.2rem !important;
  color: #6c757d !important;
  cursor: pointer;
  padding: 0.5rem !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
}

.menu-button:hover {
  background: rgba(108, 117, 125, 0.1) !important;
  transform: none !important;
}

.menu-button.active {
  background: rgba(108, 117, 125, 0.15) !important;
  color: #495057 !important;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(233, 236, 239, 0.5);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  z-index: 9999;
  min-width: 100px;
  overflow: visible;
  animation: fadeIn 0.2s ease;
  white-space: nowrap;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  display: block !important;
  width: 100% !important;
  padding: 0.75rem 1rem !important;
  background: none !important;
  border: none !important;
  text-align: left !important;
  color: #495057 !important;
  font-size: 0.875rem !important;
  cursor: pointer;
  transition: background-color 0.2s ease !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: relative;
  z-index: 10001;
}

.menu-item:hover {
  background: rgba(108, 117, 125, 0.1) !important;
  transform: none !important;
}

.menu-item:not(:last-child) {
  border-bottom: 1px solid rgba(233, 236, 239, 0.3);
}

/* 确保菜单在屏幕边缘时的显示 */
.dropdown-menu.align-left {
  right: auto;
  left: 0;
}

/* 为最后几个项目调整菜单位置 */
.todo-list li:nth-last-child(-n+2) .dropdown-menu {
  top: auto;
  bottom: calc(100% + 5px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  #root {
    padding: 1.5rem 1rem;
  }

  h1 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
  }

  .input-todo {
    flex-direction: column;
    gap: 1rem;
    padding: 1.25rem;
  }

  .todo-list li {
    flex-wrap: wrap;
    gap: 0.75rem;
    padding: 1rem;
  }

  .todo-list button {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .menu-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 1.4rem !important;
  }

  .dropdown-menu {
    min-width: 120px;
    right: -10px;
    top: calc(100% + 8px);
  }

  .menu-item {
    padding: 1rem 1.25rem !important;
    font-size: 0.9rem !important;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(233, 236, 239, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(108, 117, 125, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(108, 117, 125, 0.7);
}